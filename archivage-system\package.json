{"name": "archivage-system", "version": "1.0.0", "description": "Système d'Archivage Électronique - Documents Entrants et Sortants", "type": "module", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd backend && npm run dev", "client": "cd frontend && npm run dev", "build": "cd frontend && npm run build", "start": "cd backend && npm start", "install-all": "npm install && cd backend && npm install && cd ../frontend && npm install"}, "keywords": ["archivage", "documents", "gestion", "electronique"], "author": "Système d'Archivage", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}