
-- ------------------------------
-- Base de Données: Système d'Archivage Électronique Complet
-- Langue: Français
-- SGBD: MySQL / PostgreSQL compatible
-- Version: 2.0 - Système Complet
-- ------------------------------

-- Table des entités (organismes internes et externes)
CREATE TABLE entites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom_entite VARCHAR(255) NOT NULL,
    nom_court VARCHAR(100),
    type_entite ENUM('interne', 'externe', 'ministere', 'administration', 'entreprise', 'citoyen') DEFAULT 'externe',
    adresse TEXT,
    telephone VARCHAR(50),
    email VARCHAR(100),
    fax VARCHAR(50),
    responsable VARCHAR(100),
    actif BOOLEAN DEFAULT TRUE,
    cree_le TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modifie_le TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table des départements/services internes
CREATE TABLE services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom_service VARCHAR(100) NOT NULL,
    code_service VARCHAR(20) UNIQUE,
    responsable VARCHAR(100),
    telephone VARCHAR(50),
    email VARCHAR(100),
    actif BOOLEAN DEFAULT TRUE,
    cree_le TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des utilisateurs avec rôles étendus
CREATE TABLE utilisateurs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom_complet VARCHAR(100) NOT NULL,
    nom_utilisateur VARCHAR(50) UNIQUE,
    email VARCHAR(100) UNIQUE,
    mot_de_passe VARCHAR(255) NOT NULL,
    role ENUM('admin_systeme', 'responsable_archive', 'agent_saisie', 'consultation_seule') DEFAULT 'consultation_seule',
    id_service INT,
    telephone VARCHAR(50),
    actif BOOLEAN DEFAULT TRUE,
    derniere_connexion TIMESTAMP NULL,
    cree_le TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modifie_le TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (id_service) REFERENCES services(id)
);

-- Table des catégories de documents
CREATE TABLE categories_documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom_categorie VARCHAR(100) NOT NULL,
    description TEXT,
    couleur VARCHAR(7), -- Code couleur hex
    actif BOOLEAN DEFAULT TRUE
);

-- Table principale des documents (entrants et sortants)
CREATE TABLE documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numero_reference VARCHAR(50) NOT NULL UNIQUE,
    type_document ENUM('entrant', 'sortant') NOT NULL,
    numero_ordre INT, -- Numéro séquentiel par année
    annee INT NOT NULL,
    objet TEXT NOT NULL,
    date_document DATE NOT NULL,
    date_enregistrement DATE NOT NULL,
    date_reception DATETIME, -- Pour les entrants
    date_envoi DATETIME, -- Pour les sortants
    id_expediteur INT,
    id_destinataire INT,
    id_categorie INT,
    priorite ENUM('normale', 'urgente', 'tres_urgente') DEFAULT 'normale',
    statut ENUM('en_attente', 'en_cours', 'traite', 'archive', 'annule') DEFAULT 'en_attente',
    confidentiel BOOLEAN DEFAULT FALSE,
    nombre_pages INT DEFAULT 1,
    commentaires TEXT,
    mots_cles TEXT, -- Mots-clés séparés par des virgules
    cree_par INT NOT NULL,
    modifie_par INT,
    cree_le TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modifie_le TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (id_expediteur) REFERENCES entites(id),
    FOREIGN KEY (id_destinataire) REFERENCES entites(id),
    FOREIGN KEY (id_categorie) REFERENCES categories_documents(id),
    FOREIGN KEY (cree_par) REFERENCES utilisateurs(id),
    FOREIGN KEY (modifie_par) REFERENCES utilisateurs(id),
    INDEX idx_numero_ref (numero_reference),
    INDEX idx_type_annee (type_document, annee),
    INDEX idx_date_enreg (date_enregistrement),
    INDEX idx_statut (statut)
);

-- Table des pièces jointes
CREATE TABLE pieces_jointes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    id_document INT NOT NULL,
    nom_fichier VARCHAR(255) NOT NULL,
    nom_original VARCHAR(255),
    chemin_fichier TEXT NOT NULL,
    taille_fichier BIGINT, -- Taille en octets
    type_mime VARCHAR(100),
    extension VARCHAR(10),
    description TEXT,
    cree_par INT,
    cree_le TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (id_document) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (cree_par) REFERENCES utilisateurs(id)
);

-- Table des transferts/acheminements internes
CREATE TABLE transferts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    id_document INT NOT NULL,
    id_service_expediteur INT,
    id_service_destinataire INT,
    id_utilisateur_expediteur INT,
    id_utilisateur_destinataire INT,
    date_transfert TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_reception TIMESTAMP NULL,
    commentaire TEXT,
    instructions TEXT,
    priorite ENUM('normale', 'urgente', 'tres_urgente') DEFAULT 'normale',
    statut ENUM('envoye', 'recu', 'traite', 'retourne') DEFAULT 'envoye',
    accuse_reception BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (id_document) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (id_service_expediteur) REFERENCES services(id),
    FOREIGN KEY (id_service_destinataire) REFERENCES services(id),
    FOREIGN KEY (id_utilisateur_expediteur) REFERENCES utilisateurs(id),
    FOREIGN KEY (id_utilisateur_destinataire) REFERENCES utilisateurs(id)
);

-- Journal d'activité détaillé
CREATE TABLE journal_activite (
    id INT AUTO_INCREMENT PRIMARY KEY,
    id_utilisateur INT NOT NULL,
    action ENUM('creation', 'modification', 'suppression', 'consultation', 'transfert', 'reception', 'archivage') NOT NULL,
    table_concernee VARCHAR(50),
    id_enregistrement INT,
    id_document INT,
    details_action TEXT,
    adresse_ip VARCHAR(45),
    user_agent TEXT,
    date_action TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (id_utilisateur) REFERENCES utilisateurs(id),
    FOREIGN KEY (id_document) REFERENCES documents(id),
    INDEX idx_utilisateur_date (id_utilisateur, date_action),
    INDEX idx_document (id_document),
    INDEX idx_action (action)
);

-- Table des numéros de référence (pour génération automatique)
CREATE TABLE numerotation (
    id INT AUTO_INCREMENT PRIMARY KEY,
    type_document ENUM('entrant', 'sortant') NOT NULL,
    annee INT NOT NULL,
    dernier_numero INT DEFAULT 0,
    prefixe VARCHAR(10),
    suffixe VARCHAR(10),
    UNIQUE KEY unique_type_annee (type_document, annee)
);

-- Table des modèles de documents
CREATE TABLE modeles_documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom_modele VARCHAR(100) NOT NULL,
    type_document ENUM('entrant', 'sortant'),
    contenu_modele TEXT,
    variables_disponibles TEXT, -- JSON des variables
    actif BOOLEAN DEFAULT TRUE,
    cree_par INT,
    cree_le TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (cree_par) REFERENCES utilisateurs(id)
);

-- Table des rappels et notifications
CREATE TABLE rappels (
    id INT AUTO_INCREMENT PRIMARY KEY,
    id_document INT NOT NULL,
    id_utilisateur INT NOT NULL,
    type_rappel ENUM('echeance', 'suivi', 'reponse_attendue') NOT NULL,
    date_rappel DATETIME NOT NULL,
    message TEXT,
    envoye BOOLEAN DEFAULT FALSE,
    date_envoi TIMESTAMP NULL,
    actif BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (id_document) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (id_utilisateur) REFERENCES utilisateurs(id)
);

-- Table des paramètres système
CREATE TABLE parametres_systeme (
    id INT AUTO_INCREMENT PRIMARY KEY,
    cle_parametre VARCHAR(100) UNIQUE NOT NULL,
    valeur_parametre TEXT,
    description_parametre TEXT,
    type_parametre ENUM('string', 'integer', 'boolean', 'json') DEFAULT 'string',
    modifie_par INT,
    modifie_le TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (modifie_par) REFERENCES utilisateurs(id)
);

-- ------------------------------
-- INSERTION DES DONNÉES INITIALES
-- ------------------------------

-- Insertion des services par défaut
INSERT INTO services (nom_service, code_service, responsable) VALUES
('Direction Générale', 'DG', 'Directeur Général'),
('Secrétariat Général', 'SG', 'Secrétaire Général'),
('Service des Ressources Humaines', 'RH', 'Chef Service RH'),
('Service Financier', 'SF', 'Chef Service Financier'),
('Service Juridique', 'SJ', 'Chef Service Juridique'),
('Service Informatique', 'SI', 'Chef Service Informatique'),
('Archives', 'ARCH', 'Responsable Archives');

-- Insertion des catégories de documents
INSERT INTO categories_documents (nom_categorie, description, couleur) VALUES
('Correspondance Administrative', 'Lettres et notes administratives', '#3498db'),
('Décisions et Arrêtés', 'Décisions officielles et arrêtés', '#e74c3c'),
('Rapports', 'Rapports d\'activité et études', '#2ecc71'),
('Factures et Finances', 'Documents financiers et comptables', '#f39c12'),
('Contrats et Conventions', 'Accords et contrats', '#9b59b6'),
('Demandes Citoyens', 'Demandes et réclamations des citoyens', '#1abc9c'),
('Circulaires', 'Circulaires et instructions', '#34495e'),
('Procès-Verbaux', 'PV de réunions et commissions', '#e67e22');

-- Insertion d'entités types
INSERT INTO entites (nom_entite, nom_court, type_entite, adresse) VALUES
('Ministère de l\'Intérieur', 'MI', 'ministere', 'Rabat, Maroc'),
('Ministère des Finances', 'MF', 'ministere', 'Rabat, Maroc'),
('Wilaya de Casablanca', 'Wilaya Casa', 'administration', 'Casablanca, Maroc'),
('Préfecture de Rabat', 'Pref. Rabat', 'administration', 'Rabat, Maroc'),
('Citoyen Particulier', 'Citoyen', 'citoyen', 'Adresse variable');

-- Insertion de l'utilisateur administrateur par défaut
INSERT INTO utilisateurs (nom_complet, nom_utilisateur, email, mot_de_passe, role, id_service) VALUES
('Administrateur Système', 'admin', '<EMAIL>', '$2b$10$rQZ8kJxH.Kx8YQZ8kJxH.K', 'admin_systeme', 1),
('Responsable Archives', 'archives', '<EMAIL>', '$2b$10$rQZ8kJxH.Kx8YQZ8kJxH.K', 'responsable_archive', 7),
('Agent de Saisie', 'agent1', '<EMAIL>', '$2b$10$rQZ8kJxH.Kx8YQZ8kJxH.K', 'agent_saisie', 2);

-- Insertion des paramètres système par défaut
INSERT INTO parametres_systeme (cle_parametre, valeur_parametre, description_parametre, type_parametre) VALUES
('nom_organisation', 'Système d\'Archivage Électronique', 'Nom de l\'organisation', 'string'),
('prefixe_entrant', 'E', 'Préfixe pour documents entrants', 'string'),
('prefixe_sortant', 'S', 'Préfixe pour documents sortants', 'string'),
('taille_max_fichier', '10485760', 'Taille maximale fichier en octets (10MB)', 'integer'),
('formats_autorises', 'pdf,doc,docx,jpg,jpeg,png,txt', 'Extensions de fichiers autorisées', 'string'),
('duree_conservation', '10', 'Durée de conservation en années', 'integer'),
('notification_email', 'true', 'Activer les notifications par email', 'boolean'),
('backup_automatique', 'true', 'Sauvegarde automatique activée', 'boolean');

-- Initialisation de la numérotation pour l'année courante
INSERT INTO numerotation (type_document, annee, dernier_numero, prefixe, suffixe) VALUES
('entrant', YEAR(CURDATE()), 0, 'E', CONCAT('/', YEAR(CURDATE()))),
('sortant', YEAR(CURDATE()), 0, 'S', CONCAT('/', YEAR(CURDATE())));

-- ------------------------------
-- VUES UTILES
-- ------------------------------

-- Vue pour les documents avec détails complets
CREATE VIEW vue_documents_complets AS
SELECT
    d.id,
    d.numero_reference,
    d.type_document,
    d.objet,
    d.date_document,
    d.date_enregistrement,
    d.priorite,
    d.statut,
    d.confidentiel,
    e_exp.nom_entite AS expediteur,
    e_dest.nom_entite AS destinataire,
    c.nom_categorie AS categorie,
    u_cree.nom_complet AS cree_par_nom,
    s_cree.nom_service AS service_createur,
    d.cree_le,
    COUNT(pj.id) AS nombre_pieces_jointes
FROM documents d
LEFT JOIN entites e_exp ON d.id_expediteur = e_exp.id
LEFT JOIN entites e_dest ON d.id_destinataire = e_dest.id
LEFT JOIN categories_documents c ON d.id_categorie = c.id
LEFT JOIN utilisateurs u_cree ON d.cree_par = u_cree.id
LEFT JOIN services s_cree ON u_cree.id_service = s_cree.id
LEFT JOIN pieces_jointes pj ON d.id = pj.id_document
GROUP BY d.id;

-- Vue pour les statistiques rapides
CREATE VIEW vue_statistiques_documents AS
SELECT
    type_document,
    YEAR(date_enregistrement) AS annee,
    MONTH(date_enregistrement) AS mois,
    statut,
    COUNT(*) AS nombre_documents
FROM documents
GROUP BY type_document, YEAR(date_enregistrement), MONTH(date_enregistrement), statut;

-- ------------------------------
-- PROCÉDURES STOCKÉES
-- ------------------------------

-- Procédure pour générer le prochain numéro de référence
DELIMITER //
CREATE PROCEDURE GenererNumeroReference(
    IN p_type_document ENUM('entrant', 'sortant'),
    IN p_annee INT,
    OUT p_numero_reference VARCHAR(50)
)
BEGIN
    DECLARE v_dernier_numero INT DEFAULT 0;
    DECLARE v_prefixe VARCHAR(10);
    DECLARE v_suffixe VARCHAR(10);

    -- Vérifier si l'enregistrement existe pour cette année
    SELECT dernier_numero, prefixe, suffixe
    INTO v_dernier_numero, v_prefixe, v_suffixe
    FROM numerotation
    WHERE type_document = p_type_document AND annee = p_annee;

    -- Si pas d'enregistrement, créer un nouveau
    IF v_dernier_numero IS NULL THEN
        SELECT valeur_parametre INTO v_prefixe
        FROM parametres_systeme
        WHERE cle_parametre = CONCAT('prefixe_', p_type_document);

        SET v_suffixe = CONCAT('/', p_annee);
        SET v_dernier_numero = 0;

        INSERT INTO numerotation (type_document, annee, dernier_numero, prefixe, suffixe)
        VALUES (p_type_document, p_annee, 0, v_prefixe, v_suffixe);
    END IF;

    -- Incrémenter le numéro
    SET v_dernier_numero = v_dernier_numero + 1;

    -- Mettre à jour la table
    UPDATE numerotation
    SET dernier_numero = v_dernier_numero
    WHERE type_document = p_type_document AND annee = p_annee;

    -- Générer le numéro de référence
    SET p_numero_reference = CONCAT(v_prefixe, LPAD(v_dernier_numero, 4, '0'), v_suffixe);
END //
DELIMITER ;

-- ------------------------------
-- INDEX SUPPLÉMENTAIRES POUR PERFORMANCE
-- ------------------------------

CREATE INDEX idx_documents_recherche ON documents(objet(100), mots_cles(100));
CREATE INDEX idx_documents_dates ON documents(date_document, date_enregistrement);
CREATE INDEX idx_transferts_statut ON transferts(statut, date_transfert);
CREATE INDEX idx_journal_date_action ON journal_activite(date_action, action);
CREATE INDEX idx_pieces_jointes_type ON pieces_jointes(type_mime, extension);
