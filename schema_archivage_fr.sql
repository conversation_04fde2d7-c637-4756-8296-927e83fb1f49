
-- ------------------------------
-- Base de Données: Archivage Electronique (Entrant/Sortant)
-- Langue: Français
-- SGBD: MySQL / PostgreSQL compatible
-- ------------------------------

CREATE TABLE entites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom_entite VARCHAR(255),
    type_entite ENUM('interne', 'externe'),
    adresse TEXT,
    telephone VARCHAR(50),
    email VARCHAR(100)
);

CREATE TABLE utilisateurs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom_complet VARCHAR(100),
    email VARCHAR(100) UNIQUE,
    mot_de_passe VARCHAR(255),
    role ENUM('admin', 'agent', 'consultation'),
    actif BOOLEAN DEFAULT TRUE,
    cree_le TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numero_reference VARCHAR(50) NOT NULL,
    type_document ENUM('entrant', 'sortant') NOT NULL,
    objet TEXT,
    date_document DATE,
    date_enregistrement DATE,
    id_expediteur INT,
    id_destinataire INT,
    statut VARCHAR(50),
    commentaires TEXT,
    cree_par INT,
    cree_le TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (id_expediteur) REFERENCES entites(id),
    FOREIGN KEY (id_destinataire) REFERENCES entites(id),
    FOREIGN KEY (cree_par) REFERENCES utilisateurs(id)
);

CREATE TABLE pieces_jointes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    id_document INT,
    nom_fichier VARCHAR(255),
    chemin_fichier TEXT,
    cree_le TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (id_document) REFERENCES documents(id) ON DELETE CASCADE
);

CREATE TABLE journal_activite (
    id INT AUTO_INCREMENT PRIMARY KEY,
    id_utilisateur INT,
    action TEXT,
    id_document INT,
    date_action TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (id_utilisateur) REFERENCES utilisateurs(id),
    FOREIGN KEY (id_document) REFERENCES documents(id)
);

CREATE TABLE transferts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    id_document INT,
    de_service VARCHAR(100),
    a_service VARCHAR(100),
    date_transfert TIMESTAMP,
    commentaire TEXT,
    statut VARCHAR(50),
    FOREIGN KEY (id_document) REFERENCES documents(id)
);
