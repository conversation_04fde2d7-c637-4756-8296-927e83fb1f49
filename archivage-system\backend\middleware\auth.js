import jwt from 'jsonwebtoken';
import { executeQuery } from '../config/database.js';

// Middleware d'authentification
export const authenticateToken = async (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ 
      success: false, 
      message: 'Token d\'accès requis' 
    });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Vérifier si l'utilisateur existe toujours et est actif
    const user = await executeQuery(
      `SELECT u.*, s.nom_service, s.code_service 
       FROM utilisateurs u 
       LEFT JOIN services s ON u.id_service = s.id 
       WHERE u.id = ? AND u.actif = true`,
      [decoded.userId]
    );

    if (user.length === 0) {
      return res.status(401).json({ 
        success: false, 
        message: 'Utilisateur non trouvé ou inactif' 
      });
    }

    req.user = user[0];
    next();
  } catch (error) {
    return res.status(403).json({ 
      success: false, 
      message: 'Token invalide' 
    });
  }
};

// Middleware de vérification des rôles
export const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ 
        success: false, 
        message: 'Authentification requise' 
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ 
        success: false, 
        message: 'Permissions insuffisantes' 
      });
    }

    next();
  };
};

// Middleware pour les administrateurs uniquement
export const requireAdmin = requireRole(['admin_systeme']);

// Middleware pour les responsables d'archives et administrateurs
export const requireArchiveManager = requireRole(['admin_systeme', 'responsable_archive']);

// Middleware pour les agents de saisie et plus
export const requireAgent = requireRole(['admin_systeme', 'responsable_archive', 'agent_saisie']);

// Fonction pour générer un token JWT
export const generateToken = (user) => {
  return jwt.sign(
    { 
      userId: user.id, 
      email: user.email, 
      role: user.role 
    },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
  );
};

// Middleware pour logger les activités
export const logActivity = (action) => {
  return async (req, res, next) => {
    // Stocker l'action pour l'utiliser après la réponse
    req.activityAction = action;
    
    // Intercepter la réponse pour logger après succès
    const originalSend = res.send;
    res.send = function(data) {
      // Logger l'activité si la réponse est un succès
      if (res.statusCode < 400 && req.user) {
        logUserActivity(
          req.user.id,
          req.activityAction,
          req.body?.id || req.params?.id,
          req.ip,
          req.get('User-Agent')
        ).catch(console.error);
      }
      
      originalSend.call(this, data);
    };
    
    next();
  };
};

// Fonction pour enregistrer l'activité utilisateur
const logUserActivity = async (userId, action, documentId, ip, userAgent) => {
  try {
    await executeQuery(
      `INSERT INTO journal_activite 
       (id_utilisateur, action, id_document, adresse_ip, user_agent) 
       VALUES (?, ?, ?, ?, ?)`,
      [userId, action, documentId, ip, userAgent]
    );
  } catch (error) {
    console.error('Erreur lors de l\'enregistrement de l\'activité:', error);
  }
};
