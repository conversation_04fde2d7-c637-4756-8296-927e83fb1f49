import { executeQuery } from '../config/database.js';
import bcrypt from 'bcryptjs';

export class User {
  // إنشاء مستخدم جديد
  static async create(userData) {
    try {
      // تشفير كلمة المرور
      const hashedPassword = await bcrypt.hash(userData.mot_de_passe, 12);

      const query = `
        INSERT INTO utilisateurs (
          nom_complet, nom_utilisateur, email, mot_de_passe, 
          role, id_service, telephone
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `;

      const params = [
        userData.nom_complet,
        userData.nom_utilisateur,
        userData.email,
        hashedPassword,
        userData.role || 'consultation_seule',
        userData.id_service,
        userData.telephone
      ];

      const result = await executeQuery(query, params);
      return { id: result.insertId };
    } catch (error) {
      if (error.code === 'ER_DUP_ENTRY') {
        throw new Error('Nom d\'utilisateur ou email déjà existant');
      }
      throw new Error(`Erreur lors de la création de l'utilisateur: ${error.message}`);
    }
  }

  // Authentification utilisateur
  static async authenticate(identifier, password) {
    try {
      const query = `
        SELECT u.*, s.nom_service, s.code_service
        FROM utilisateurs u
        LEFT JOIN services s ON u.id_service = s.id
        WHERE (u.email = ? OR u.nom_utilisateur = ?) AND u.actif = true
      `;

      const users = await executeQuery(query, [identifier, identifier]);
      
      if (users.length === 0) {
        return null;
      }

      const user = users[0];
      const isValidPassword = await bcrypt.compare(password, user.mot_de_passe);
      
      if (!isValidPassword) {
        return null;
      }

      // Mettre à jour la dernière connexion
      await this.updateLastLogin(user.id);

      // Retourner l'utilisateur sans le mot de passe
      const { mot_de_passe, ...userWithoutPassword } = user;
      return userWithoutPassword;
    } catch (error) {
      throw new Error(`Erreur lors de l'authentification: ${error.message}`);
    }
  }

  // Récupérer un utilisateur par ID
  static async findById(id) {
    try {
      const query = `
        SELECT u.*, s.nom_service, s.code_service
        FROM utilisateurs u
        LEFT JOIN services s ON u.id_service = s.id
        WHERE u.id = ?
      `;

      const users = await executeQuery(query, [id]);
      
      if (users.length === 0) {
        return null;
      }

      const { mot_de_passe, ...userWithoutPassword } = users[0];
      return userWithoutPassword;
    } catch (error) {
      throw new Error(`Erreur lors de la récupération de l'utilisateur: ${error.message}`);
    }
  }

  // Récupérer tous les utilisateurs avec pagination
  static async findAll(page = 1, limit = 20, filters = {}) {
    try {
      let whereConditions = ['u.id IS NOT NULL'];
      let params = [];

      // Filtres
      if (filters.role) {
        whereConditions.push('u.role = ?');
        params.push(filters.role);
      }

      if (filters.id_service) {
        whereConditions.push('u.id_service = ?');
        params.push(filters.id_service);
      }

      if (filters.actif !== undefined) {
        whereConditions.push('u.actif = ?');
        params.push(filters.actif);
      }

      if (filters.search) {
        whereConditions.push('(u.nom_complet LIKE ? OR u.email LIKE ? OR u.nom_utilisateur LIKE ?)');
        params.push(`%${filters.search}%`, `%${filters.search}%`, `%${filters.search}%`);
      }

      const whereClause = 'WHERE ' + whereConditions.join(' AND ');

      // Compter le total
      const countQuery = `
        SELECT COUNT(*) as total
        FROM utilisateurs u
        ${whereClause}
      `;

      const countResult = await executeQuery(countQuery, params);
      const total = countResult[0].total;

      // Récupérer les données avec pagination
      const offset = (page - 1) * limit;
      const dataQuery = `
        SELECT u.id, u.nom_complet, u.nom_utilisateur, u.email, u.role,
               u.telephone, u.actif, u.derniere_connexion, u.cree_le,
               s.nom_service, s.code_service
        FROM utilisateurs u
        LEFT JOIN services s ON u.id_service = s.id
        ${whereClause}
        ORDER BY u.nom_complet
        LIMIT ? OFFSET ?
      `;

      params.push(limit, offset);
      const users = await executeQuery(dataQuery, params);

      return {
        users,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw new Error(`Erreur lors de la récupération des utilisateurs: ${error.message}`);
    }
  }

  // Mettre à jour un utilisateur
  static async update(id, updateData) {
    try {
      const setClause = [];
      const params = [];

      // Construction dynamique de la clause SET
      Object.keys(updateData).forEach(key => {
        if (updateData[key] !== undefined && key !== 'id' && key !== 'mot_de_passe') {
          setClause.push(`${key} = ?`);
          params.push(updateData[key]);
        }
      });

      // Gestion spéciale pour le mot de passe
      if (updateData.mot_de_passe) {
        const hashedPassword = await bcrypt.hash(updateData.mot_de_passe, 12);
        setClause.push('mot_de_passe = ?');
        params.push(hashedPassword);
      }

      if (setClause.length === 0) {
        throw new Error('Aucune donnée à mettre à jour');
      }

      setClause.push('modifie_le = NOW()');
      params.push(id);

      const query = `
        UPDATE utilisateurs 
        SET ${setClause.join(', ')}
        WHERE id = ?
      `;

      const result = await executeQuery(query, params);
      return result.affectedRows > 0;
    } catch (error) {
      if (error.code === 'ER_DUP_ENTRY') {
        throw new Error('Nom d\'utilisateur ou email déjà existant');
      }
      throw new Error(`Erreur lors de la mise à jour: ${error.message}`);
    }
  }

  // Supprimer un utilisateur (désactivation)
  static async delete(id) {
    try {
      const query = 'UPDATE utilisateurs SET actif = false WHERE id = ?';
      const result = await executeQuery(query, [id]);
      return result.affectedRows > 0;
    } catch (error) {
      throw new Error(`Erreur lors de la suppression: ${error.message}`);
    }
  }

  // Mettre à jour la dernière connexion
  static async updateLastLogin(id) {
    try {
      const query = 'UPDATE utilisateurs SET derniere_connexion = NOW() WHERE id = ?';
      await executeQuery(query, [id]);
    } catch (error) {
      console.error('Erreur lors de la mise à jour de la dernière connexion:', error);
    }
  }

  // Changer le mot de passe
  static async changePassword(id, currentPassword, newPassword) {
    try {
      // Vérifier le mot de passe actuel
      const user = await executeQuery(
        'SELECT mot_de_passe FROM utilisateurs WHERE id = ?',
        [id]
      );

      if (user.length === 0) {
        throw new Error('Utilisateur non trouvé');
      }

      const isValidPassword = await bcrypt.compare(currentPassword, user[0].mot_de_passe);
      
      if (!isValidPassword) {
        throw new Error('Mot de passe actuel incorrect');
      }

      // Mettre à jour avec le nouveau mot de passe
      const hashedNewPassword = await bcrypt.hash(newPassword, 12);
      const query = 'UPDATE utilisateurs SET mot_de_passe = ? WHERE id = ?';
      const result = await executeQuery(query, [hashedNewPassword, id]);
      
      return result.affectedRows > 0;
    } catch (error) {
      throw new Error(`Erreur lors du changement de mot de passe: ${error.message}`);
    }
  }

  // Obtenir les statistiques des utilisateurs
  static async getStatistics() {
    try {
      const queries = {
        // Total par rôle
        totalParRole: `
          SELECT role, COUNT(*) as total
          FROM utilisateurs
          WHERE actif = true
          GROUP BY role
        `,
        
        // Total par service
        totalParService: `
          SELECT s.nom_service, COUNT(u.id) as total
          FROM services s
          LEFT JOIN utilisateurs u ON s.id = u.id_service AND u.actif = true
          GROUP BY s.id, s.nom_service
          ORDER BY total DESC
        `,
        
        // Connexions récentes (derniers 30 jours)
        connexionsRecentes: `
          SELECT 
            DATE(derniere_connexion) as date_connexion,
            COUNT(*) as total_connexions
          FROM utilisateurs
          WHERE derniere_connexion >= DATE_SUB(NOW(), INTERVAL 30 DAY)
          GROUP BY DATE(derniere_connexion)
          ORDER BY date_connexion DESC
        `,
        
        // Utilisateurs actifs vs inactifs
        statutActivite: `
          SELECT 
            CASE WHEN actif THEN 'Actif' ELSE 'Inactif' END as statut,
            COUNT(*) as total
          FROM utilisateurs
          GROUP BY actif
        `
      };

      const results = {};
      for (const [key, query] of Object.entries(queries)) {
        results[key] = await executeQuery(query);
      }

      return results;
    } catch (error) {
      throw new Error(`Erreur lors de la récupération des statistiques: ${error.message}`);
    }
  }

  // Vérifier les permissions
  static hasPermission(userRole, requiredRoles) {
    const roleHierarchy = {
      'admin_systeme': 4,
      'responsable_archive': 3,
      'agent_saisie': 2,
      'consultation_seule': 1
    };

    const userLevel = roleHierarchy[userRole] || 0;
    const requiredLevel = Math.max(...requiredRoles.map(role => roleHierarchy[role] || 0));
    
    return userLevel >= requiredLevel;
  }
}
