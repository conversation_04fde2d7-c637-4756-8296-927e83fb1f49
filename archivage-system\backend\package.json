{"name": "archivage-backend", "version": "1.0.0", "description": "Backend API pour le système d'archivage électronique", "type": "module", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "joi": "^17.11.0", "moment": "^2.29.4", "nodemailer": "^6.9.7", "uuid": "^9.0.1", "compression": "^1.7.4"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}}