import { executeQuery, executeTransaction } from '../config/database.js';
import moment from 'moment';

export class Document {
  // إنشاء وثيقة جديدة
  static async create(documentData, userId) {
    try {
      // توليد رقم المرجع
      const numeroReference = await this.generateReference(
        documentData.type_document,
        new Date().getFullYear()
      );

      const query = `
        INSERT INTO documents (
          numero_reference, type_document, annee, objet, date_document,
          date_enregistrement, date_reception, date_envoi, id_expediteur,
          id_destinataire, id_categorie, priorite, statut, confidentiel,
          nombre_pages, commentaires, mots_cles, cree_par
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const params = [
        numeroReference,
        documentData.type_document,
        new Date().getFullYear(),
        documentData.objet,
        documentData.date_document,
        documentData.date_enregistrement || new Date(),
        documentData.date_reception,
        documentData.date_envoi,
        documentData.id_expediteur,
        documentData.id_destinataire,
        documentData.id_categorie,
        documentData.priorite || 'normale',
        documentData.statut || 'en_attente',
        documentData.confidentiel || false,
        documentData.nombre_pages || 1,
        documentData.commentaires,
        documentData.mots_cles,
        userId
      ];

      const result = await executeQuery(query, params);
      return { id: result.insertId, numero_reference: numeroReference };
    } catch (error) {
      throw new Error(`Erreur lors de la création du document: ${error.message}`);
    }
  }

  // Récupérer un document par ID
  static async findById(id) {
    try {
      const query = `
        SELECT d.*, 
               e_exp.nom_entite as expediteur_nom,
               e_dest.nom_entite as destinataire_nom,
               c.nom_categorie as categorie_nom,
               u.nom_complet as cree_par_nom,
               s.nom_service as service_nom
        FROM documents d
        LEFT JOIN entites e_exp ON d.id_expediteur = e_exp.id
        LEFT JOIN entites e_dest ON d.id_destinataire = e_dest.id
        LEFT JOIN categories_documents c ON d.id_categorie = c.id
        LEFT JOIN utilisateurs u ON d.cree_par = u.id
        LEFT JOIN services s ON u.id_service = s.id
        WHERE d.id = ?
      `;
      
      const results = await executeQuery(query, [id]);
      return results[0] || null;
    } catch (error) {
      throw new Error(`Erreur lors de la récupération du document: ${error.message}`);
    }
  }

  // Rechercher des documents avec filtres
  static async search(filters = {}, page = 1, limit = 20) {
    try {
      let whereConditions = [];
      let params = [];

      // Construction dynamique des conditions WHERE
      if (filters.type_document) {
        whereConditions.push('d.type_document = ?');
        params.push(filters.type_document);
      }

      if (filters.numero_reference) {
        whereConditions.push('d.numero_reference LIKE ?');
        params.push(`%${filters.numero_reference}%`);
      }

      if (filters.objet) {
        whereConditions.push('d.objet LIKE ?');
        params.push(`%${filters.objet}%`);
      }

      if (filters.statut) {
        whereConditions.push('d.statut = ?');
        params.push(filters.statut);
      }

      if (filters.id_categorie) {
        whereConditions.push('d.id_categorie = ?');
        params.push(filters.id_categorie);
      }

      if (filters.date_debut) {
        whereConditions.push('d.date_enregistrement >= ?');
        params.push(filters.date_debut);
      }

      if (filters.date_fin) {
        whereConditions.push('d.date_enregistrement <= ?');
        params.push(filters.date_fin);
      }

      if (filters.id_expediteur) {
        whereConditions.push('d.id_expediteur = ?');
        params.push(filters.id_expediteur);
      }

      if (filters.id_destinataire) {
        whereConditions.push('d.id_destinataire = ?');
        params.push(filters.id_destinataire);
      }

      if (filters.mots_cles) {
        whereConditions.push('(d.mots_cles LIKE ? OR d.objet LIKE ?)');
        params.push(`%${filters.mots_cles}%`, `%${filters.mots_cles}%`);
      }

      const whereClause = whereConditions.length > 0 
        ? 'WHERE ' + whereConditions.join(' AND ') 
        : '';

      // Requête pour compter le total
      const countQuery = `
        SELECT COUNT(*) as total
        FROM documents d
        ${whereClause}
      `;

      const countResult = await executeQuery(countQuery, params);
      const total = countResult[0].total;

      // Requête pour récupérer les données avec pagination
      const offset = (page - 1) * limit;
      const dataQuery = `
        SELECT d.*, 
               e_exp.nom_entite as expediteur_nom,
               e_dest.nom_entite as destinataire_nom,
               c.nom_categorie as categorie_nom,
               u.nom_complet as cree_par_nom,
               s.nom_service as service_nom,
               COUNT(pj.id) as nombre_pieces_jointes
        FROM documents d
        LEFT JOIN entites e_exp ON d.id_expediteur = e_exp.id
        LEFT JOIN entites e_dest ON d.id_destinataire = e_dest.id
        LEFT JOIN categories_documents c ON d.id_categorie = c.id
        LEFT JOIN utilisateurs u ON d.cree_par = u.id
        LEFT JOIN services s ON u.id_service = s.id
        LEFT JOIN pieces_jointes pj ON d.id = pj.id_document
        ${whereClause}
        GROUP BY d.id
        ORDER BY d.date_enregistrement DESC
        LIMIT ? OFFSET ?
      `;

      params.push(limit, offset);
      const documents = await executeQuery(dataQuery, params);

      return {
        documents,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw new Error(`Erreur lors de la recherche: ${error.message}`);
    }
  }

  // Mettre à jour un document
  static async update(id, updateData, userId) {
    try {
      const setClause = [];
      const params = [];

      // Construction dynamique de la clause SET
      Object.keys(updateData).forEach(key => {
        if (updateData[key] !== undefined && key !== 'id') {
          setClause.push(`${key} = ?`);
          params.push(updateData[key]);
        }
      });

      if (setClause.length === 0) {
        throw new Error('Aucune donnée à mettre à jour');
      }

      setClause.push('modifie_par = ?', 'modifie_le = NOW()');
      params.push(userId, id);

      const query = `
        UPDATE documents 
        SET ${setClause.join(', ')}
        WHERE id = ?
      `;

      const result = await executeQuery(query, params);
      return result.affectedRows > 0;
    } catch (error) {
      throw new Error(`Erreur lors de la mise à jour: ${error.message}`);
    }
  }

  // Supprimer un document
  static async delete(id) {
    try {
      const query = 'DELETE FROM documents WHERE id = ?';
      const result = await executeQuery(query, [id]);
      return result.affectedRows > 0;
    } catch (error) {
      throw new Error(`Erreur lors de la suppression: ${error.message}`);
    }
  }

  // Générer un numéro de référence automatique
  static async generateReference(typeDocument, annee) {
    try {
      // Appeler la procédure stockée pour générer le numéro
      const query = 'CALL GenererNumeroReference(?, ?, @numero_reference)';
      await executeQuery(query, [typeDocument, annee]);
      
      const result = await executeQuery('SELECT @numero_reference as numero_reference');
      return result[0].numero_reference;
    } catch (error) {
      // Fallback en cas d'erreur avec la procédure stockée
      const prefix = typeDocument === 'entrant' ? 'E' : 'S';
      const countQuery = `
        SELECT COUNT(*) as count 
        FROM documents 
        WHERE type_document = ? AND annee = ?
      `;
      const countResult = await executeQuery(countQuery, [typeDocument, annee]);
      const nextNumber = countResult[0].count + 1;
      
      return `${prefix}${String(nextNumber).padStart(4, '0')}/${annee}`;
    }
  }

  // Obtenir les statistiques des documents
  static async getStatistics(filters = {}) {
    try {
      const queries = {
        // Total par type
        totalParType: `
          SELECT type_document, COUNT(*) as total
          FROM documents
          GROUP BY type_document
        `,
        
        // Total par statut
        totalParStatut: `
          SELECT statut, COUNT(*) as total
          FROM documents
          GROUP BY statut
        `,
        
        // Documents par mois (derniers 12 mois)
        documentsParMois: `
          SELECT 
            YEAR(date_enregistrement) as annee,
            MONTH(date_enregistrement) as mois,
            type_document,
            COUNT(*) as total
          FROM documents
          WHERE date_enregistrement >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
          GROUP BY YEAR(date_enregistrement), MONTH(date_enregistrement), type_document
          ORDER BY annee DESC, mois DESC
        `,
        
        // Top 5 des expéditeurs/destinataires
        topEntites: `
          SELECT 
            e.nom_entite,
            COUNT(d.id) as total_documents
          FROM entites e
          INNER JOIN documents d ON (e.id = d.id_expediteur OR e.id = d.id_destinataire)
          GROUP BY e.id, e.nom_entite
          ORDER BY total_documents DESC
          LIMIT 5
        `
      };

      const results = {};
      for (const [key, query] of Object.entries(queries)) {
        results[key] = await executeQuery(query);
      }

      return results;
    } catch (error) {
      throw new Error(`Erreur lors de la récupération des statistiques: ${error.message}`);
    }
  }

  // Transférer un document vers un service
  static async transfer(documentId, transferData, userId) {
    try {
      const transferQuery = `
        INSERT INTO transferts (
          id_document, id_service_expediteur, id_service_destinataire,
          id_utilisateur_expediteur, commentaire, instructions, priorite
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `;

      const transferParams = [
        documentId,
        transferData.id_service_expediteur,
        transferData.id_service_destinataire,
        userId,
        transferData.commentaire,
        transferData.instructions,
        transferData.priorite || 'normale'
      ];

      const result = await executeQuery(transferQuery, transferParams);
      return { id: result.insertId };
    } catch (error) {
      throw new Error(`Erreur lors du transfert: ${error.message}`);
    }
  }
}
