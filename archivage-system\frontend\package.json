{"name": "archivage-frontend", "version": "1.0.0", "description": "Interface utilisateur pour le système d'archivage électronique", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "axios": "^1.6.2", "antd": "^5.12.8", "@ant-design/icons": "^5.2.6", "moment": "^2.29.4", "react-query": "^3.39.3", "zustand": "^4.4.7", "react-hook-form": "^7.48.2", "recharts": "^2.8.0", "react-pdf": "^7.6.0", "file-saver": "^2.0.5"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "vite": "^5.0.8"}}